// ==UserScript==
// @name         打字测试自动化脚本
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动化打字测试，模拟真人输入
// <AUTHOR>
// @match        https://dazi.kukuw.com/*
// @match        http://dazi.kukuw.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    let isRunning = false;
    let targetSpeed = 60; // 默认目标速度 WPM
    let currentIndex = 0;
    let typingInterval;
    let controlPanel;

    // 速度调节相关变量
    let currentActualSpeed = 0;
    let speedAdjustmentFactor = 1.0; // 速度调节因子
    let lastSpeedCheckTime = 0;
    let speedHistory = []; // 速度历史记录
    let adjustmentStatus = '初始化'; // 调节状态

    // 文章进度和时间控制变量
    let totalCharacters = 0; // 文章总字符数
    let typedCharacters = 0; // 已输入字符数
    let startTime = 0; // 开始时间
    let targetTimeMinutes = 5; // 目标时间（分钟）
    let isReverseControl = false; // 是否启用反向控制

    // 创建控制面板
    function createControlPanel() {
        controlPanel = document.createElement('div');
        controlPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 280px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;

        controlPanel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #4CAF50;">打字自动化控制台</div>
            <div style="margin-bottom: 8px;">
                <label>目标速度 (WPM): </label>
                <input type="number" id="speedInput" value="60" min="10" max="200" style="width: 60px; padding: 2px;">
            </div>
            <div style="margin-bottom: 10px;">
                <button id="startBtn" style="padding: 5px 10px; margin-right: 5px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">开始</button>
                <button id="stopBtn" style="padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer;">停止</button>
            </div>
            <div id="statusInfo" style="font-size: 11px; line-height: 1.4;">
                <div>状态: <span id="status">待机</span></div>
                <div>目标速度: <span id="targetSpeedDisplay">60</span> WPM</div>
                <div>当前速度: <span id="currentSpeed">-</span> WPM</div>
                <div>速度调节: <span id="speedAdjustment">待机</span></div>
                <div>调节因子: <span id="adjustmentFactor">1.00</span></div>
                <div>进度控制: <span id="progressControl">正常</span></div>
                <div>预计完成: <span id="estimatedTime">-</span></div>
                <div>正确率: <span id="accuracy">-</span>%</div>
                <div>剩余时间: <span id="timeLeft">-</span></div>
                <div>当前行: <span id="currentLine">0</span></div>
            </div>
        `;

        document.body.appendChild(controlPanel);

        // 绑定事件
        document.getElementById('startBtn').addEventListener('click', startTyping);
        document.getElementById('stopBtn').addEventListener('click', stopTyping);
        document.getElementById('speedInput').addEventListener('change', function() {
            targetSpeed = parseInt(this.value) || 60;
            document.getElementById('targetSpeedDisplay').textContent = targetSpeed;
        });
    }

    // 计算文章总字符数
    function calculateTotalCharacters() {
        let total = 0;
        const typingDivs = document.querySelectorAll('.typing');
        typingDivs.forEach(div => {
            const hiddenInput = div.querySelector('input[type="hidden"]');
            if (hiddenInput && hiddenInput.value) {
                total += hiddenInput.value.length;
            }
        });
        return total;
    }

    // 获取目标时间（从页面读取）
    function getTargetTime() {
        const timeElement = document.querySelector('.sheding');
        if (timeElement) {
            const timeText = timeElement.textContent;
            const timeMatch = timeText.match(/(\d+)/);
            if (timeMatch) {
                return parseInt(timeMatch[1]);
            }
        }
        return 5; // 默认5分钟
    }

    // 预测完成时间和进度控制
    function calculateProgressControl() {
        if (!startTime || typedCharacters === 0) return;

        const elapsedMinutes = (Date.now() - startTime) / (1000 * 60);
        const progress = typedCharacters / totalCharacters;
        const estimatedTotalMinutes = elapsedMinutes / progress;
        const targetMinutes = targetTimeMinutes;

        // 更新显示
        document.getElementById('estimatedTime').textContent =
            `${estimatedTotalMinutes.toFixed(1)}/${targetMinutes}分钟`;

        // 判断是否需要反向控制
        const timeBuffer = targetMinutes * 0.1; // 10%的时间缓冲

        if (estimatedTotalMinutes < targetMinutes - timeBuffer) {
            // 预计完成时间过早，需要减速
            isReverseControl = true;
            adjustmentStatus = '主动减速';

            // 计算需要的减速程度
            const timeRatio = estimatedTotalMinutes / targetMinutes;
            const slowdownFactor = Math.max(1.2, 2.0 - timeRatio);
            speedAdjustmentFactor = Math.min(speedAdjustmentFactor * slowdownFactor, 3.0);

            document.getElementById('progressControl').textContent = '减速控制';
            document.getElementById('progressControl').style.color = '#FF5722';

        } else if (estimatedTotalMinutes > targetMinutes + timeBuffer) {
            // 预计完成时间过晚，需要加速
            isReverseControl = false;
            adjustmentStatus = '追赶进度';

            const timeRatio = targetMinutes / estimatedTotalMinutes;
            const speedupFactor = Math.max(0.8, timeRatio);
            speedAdjustmentFactor = Math.max(speedAdjustmentFactor * speedupFactor, 0.3);

            document.getElementById('progressControl').textContent = '加速追赶';
            document.getElementById('progressControl').style.color = '#FF9800';

        } else {
            // 时间控制正常
            isReverseControl = false;
            document.getElementById('progressControl').textContent = '时间正常';
            document.getElementById('progressControl').style.color = '#4CAF50';
        }
    }

    // 获取当前需要输入的文本
    function getCurrentText() {
        const currentDiv = document.querySelector('.typing_on');
        if (!currentDiv) return null;
        
        const textSpan = currentDiv.querySelector('.text span');
        return textSpan ? textSpan.textContent : null;
    }

    // 获取当前输入框
    function getCurrentInput() {
        const currentDiv = document.querySelector('.typing_on');
        if (!currentDiv) return null;
        
        return currentDiv.querySelector('input[type="text"]');
    }

    // 动态速度调节函数（增强版，包含反向控制）
    function adjustSpeed() {
        const speedElement = document.querySelector('.sudu');
        if (!speedElement) return;

        const speedText = speedElement.textContent;
        const speedMatch = speedText.match(/(\d+(?:\.\d+)?)/);

        if (speedMatch) {
            currentActualSpeed = parseFloat(speedMatch[1]);
            speedHistory.push(currentActualSpeed);

            // 保持最近10次的速度记录
            if (speedHistory.length > 10) {
                speedHistory.shift();
            }

            // 计算平均速度（最近5次）
            const recentSpeeds = speedHistory.slice(-5);
            const avgSpeed = recentSpeeds.reduce((a, b) => a + b, 0) / recentSpeeds.length;

            // 首先进行进度控制检查
            calculateProgressControl();

            // 如果启用了反向控制，优先考虑时间控制
            if (isReverseControl) {
                // 反向控制模式：主要关注时间而非速度
                return;
            }

            // 正常速度调节模式
            const speedDiff = targetSpeed - avgSpeed;
            const tolerance = targetSpeed * 0.05; // 5%的容差

            if (Math.abs(speedDiff) <= tolerance) {
                if (adjustmentStatus !== '主动减速' && adjustmentStatus !== '追赶进度') {
                    adjustmentStatus = '速度稳定';
                }
            } else if (speedDiff > tolerance) {
                // 当前速度低于目标，需要加速
                if (adjustmentStatus !== '主动减速') {
                    adjustmentStatus = '加速中';
                    speedAdjustmentFactor *= 0.95; // 减少延迟
                    speedAdjustmentFactor = Math.max(speedAdjustmentFactor, 0.3); // 最小值限制
                }
            } else {
                // 当前速度高于目标，需要减速
                if (adjustmentStatus !== '追赶进度') {
                    adjustmentStatus = '减速中';
                    speedAdjustmentFactor *= 1.05; // 增加延迟
                    speedAdjustmentFactor = Math.min(speedAdjustmentFactor, 3.0); // 最大值限制
                }
            }

            lastSpeedCheckTime = Date.now();
        }
    }

    // 智能速度计算（考虑文章进度和剩余时间）
    function calculateSmartDelay(baseDelay) {
        let finalDelay = baseDelay * speedAdjustmentFactor;

        // 计算文章进度
        const progress = typedCharacters / totalCharacters;

        // 如果接近文章末尾（最后20%），更精确地控制速度
        if (progress > 0.8) {
            const elapsedMinutes = (Date.now() - startTime) / (1000 * 60);
            const remainingMinutes = targetTimeMinutes - elapsedMinutes;
            const remainingChars = totalCharacters - typedCharacters;

            if (remainingMinutes > 0 && remainingChars > 0) {
                // 计算需要的速度来达到目标时间
                const neededWPM = (remainingChars / 5) / remainingMinutes; // 假设平均5字符/词
                const neededDelay = 60000 / (neededWPM * 5);

                // 平滑过渡到需要的延迟
                const blendFactor = (progress - 0.8) / 0.2; // 0到1的渐变
                finalDelay = finalDelay * (1 - blendFactor) + neededDelay * blendFactor;

                adjustmentStatus = '精确控制';
            }
        }

        return finalDelay;
    }

    // 模拟真人输入（带智能速度调节和进度控制）
    function simulateTyping(text, input, callback) {
        let index = 0;
        const baseDelay = 60000 / (targetSpeed * 5); // 基础延迟，考虑平均字符长度

        function typeNextChar() {
            if (index >= text.length || !isRunning) {
                // 更新已输入字符数
                typedCharacters += index;
                if (callback) callback();
                return;
            }

            const char = text[index];

            // 使用智能延迟计算
            const smartDelay = calculateSmartDelay(baseDelay, index, text.length);

            // 模拟真人输入的随机延迟
            const randomFactor = 0.85 + Math.random() * 0.3; // 0.85-1.15倍的随机因子
            const delay = smartDelay * randomFactor;

            // 某些字符可能需要更长时间
            const extraDelay = /[，。！？；：""''（）]/.test(char) ? delay * 0.15 : 0;

            setTimeout(() => {
                // 模拟按键事件
                const keydownEvent = new KeyboardEvent('keydown', {
                    key: char,
                    code: `Key${char.toUpperCase()}`,
                    bubbles: true
                });

                const inputEvent = new InputEvent('input', {
                    data: char,
                    bubbles: true
                });

                input.dispatchEvent(keydownEvent);
                input.value += char;
                input.dispatchEvent(inputEvent);

                index++;

                // 每输入几个字符检查一次速度和进度
                if (index % 3 === 0) {
                    adjustSpeed();
                }

                typeNextChar();
            }, delay + extraDelay);
        }

        typeNextChar();
    }

    // 移动到下一行
    function moveToNextLine() {
        const currentInput = getCurrentInput();
        if (currentInput) {
            // 模拟回车键
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                bubbles: true
            });
            currentInput.dispatchEvent(enterEvent);
            
            // 触发表单提交或其他必要的事件
            const form = document.getElementById('info_form');
            if (form) {
                const submitEvent = new Event('submit', { bubbles: true });
                form.dispatchEvent(submitEvent);
            }
        }
    }

    // 更新状态信息
    function updateStatus() {
        if (!controlPanel) return;

        // 获取右侧信息
        const speedElement = document.querySelector('.sudu');
        const accuracyElement = document.querySelector('.zhengquelv');
        const timeElement = document.querySelector('.daojishi_time');

        const currentSpeedText = speedElement ? speedElement.textContent : '';
        const accuracyText = accuracyElement ? accuracyElement.textContent : '';
        const timeText = timeElement ? timeElement.textContent : '';

        // 提取数值
        const speedMatch = currentSpeedText.match(/(\d+(?:\.\d+)?)/);
        const accuracyMatch = accuracyText.match(/(\d+(?:\.\d+)?)/);

        document.getElementById('currentSpeed').textContent = speedMatch ? speedMatch[1] : '-';
        document.getElementById('accuracy').textContent = accuracyMatch ? accuracyMatch[1] : '-';
        document.getElementById('timeLeft').textContent = timeText || '-';
        document.getElementById('currentLine').textContent = currentIndex + 1;

        // 更新速度调节信息
        document.getElementById('speedAdjustment').textContent = adjustmentStatus;
        document.getElementById('adjustmentFactor').textContent = speedAdjustmentFactor.toFixed(2);

        // 根据调节状态设置颜色
        const adjustmentElement = document.getElementById('speedAdjustment');
        if (adjustmentStatus === '速度稳定') {
            adjustmentElement.style.color = '#4CAF50';
        } else if (adjustmentStatus === '加速中' || adjustmentStatus === '追赶进度') {
            adjustmentElement.style.color = '#FF9800';
        } else if (adjustmentStatus === '减速中' || adjustmentStatus === '主动减速') {
            adjustmentElement.style.color = '#2196F3';
        } else if (adjustmentStatus === '精确控制') {
            adjustmentElement.style.color = '#9C27B0';
        } else {
            adjustmentElement.style.color = '#FFF';
        }
    }

    // 开始打字
    function startTyping() {
        if (isRunning) return;

        isRunning = true;
        currentIndex = 0;

        // 初始化文章信息
        totalCharacters = calculateTotalCharacters();
        typedCharacters = 0;
        startTime = Date.now();
        targetTimeMinutes = getTargetTime();

        // 重置速度调节相关变量
        speedAdjustmentFactor = 1.0;
        speedHistory = [];
        adjustmentStatus = '初始化';
        currentActualSpeed = 0;
        lastSpeedCheckTime = Date.now();
        isReverseControl = false;

        document.getElementById('status').textContent = '运行中';
        console.log(`文章总字符数: ${totalCharacters}, 目标时间: ${targetTimeMinutes}分钟`);
        
        function typeCurrentLine() {
            if (!isRunning) return;
            
            const text = getCurrentText();
            const input = getCurrentInput();
            
            if (!text || !input) {
                console.log('无法找到当前文本或输入框');
                stopTyping();
                return;
            }
            
            // 清空输入框
            input.value = '';
            
            // 开始输入当前行
            simulateTyping(text, input, () => {
                if (!isRunning) return;
                
                // 等待一小段时间后移动到下一行
                setTimeout(() => {
                    moveToNextLine();
                    currentIndex++;
                    
                    // 等待页面更新后继续下一行
                    setTimeout(() => {
                        if (isRunning && getCurrentText()) {
                            typeCurrentLine();
                        } else {
                            stopTyping();
                        }
                    }, 500);
                }, 200 + Math.random() * 300); // 随机延迟
            });
        }
        
        // 开始输入
        typeCurrentLine();
        
        // 定期更新状态和调节速度
        const statusInterval = setInterval(() => {
            if (!isRunning) {
                clearInterval(statusInterval);
                return;
            }
            updateStatus();

            // 每1.5秒进行一次速度调节检查
            if (Date.now() - lastSpeedCheckTime > 1500) {
                adjustSpeed();
            }

            // 更新进度控制
            if (totalCharacters > 0 && typedCharacters > 0) {
                calculateProgressControl();
            }
        }, 1000);
    }

    // 停止打字
    function stopTyping() {
        isRunning = false;
        if (typingInterval) {
            clearInterval(typingInterval);
        }

        // 重置状态
        adjustmentStatus = '已停止';
        document.getElementById('status').textContent = '已停止';
        document.getElementById('speedAdjustment').textContent = '已停止';
        document.getElementById('speedAdjustment').style.color = '#FFF';
    }

    // 初始化
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        // 检查是否在打字页面
        if (document.querySelector('.typing_content')) {
            createControlPanel();
            console.log('打字自动化脚本已加载');
        }
    }

    // 启动脚本
    init();

})();
