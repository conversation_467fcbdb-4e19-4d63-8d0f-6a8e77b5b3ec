// ==UserScript==
// @name         打字测试自动化脚本
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动化打字测试，模拟真人输入
// <AUTHOR>
// @match        https://dazi.kukuw.com/*
// @match        http://dazi.kukuw.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    let isRunning = false;
    let targetSpeed = 60; // 默认目标速度 WPM
    let currentIndex = 0;
    let typingInterval;
    let controlPanel;

    // 速度调节相关变量
    let currentActualSpeed = 0;
    let speedAdjustmentFactor = 1.0; // 速度调节因子
    let lastSpeedCheckTime = 0;
    let speedHistory = []; // 速度历史记录
    let adjustmentStatus = '初始化'; // 调节状态

    // 创建控制面板
    function createControlPanel() {
        controlPanel = document.createElement('div');
        controlPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 280px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;

        controlPanel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #4CAF50;">打字自动化控制台</div>
            <div style="margin-bottom: 8px;">
                <label>目标速度 (WPM): </label>
                <input type="number" id="speedInput" value="60" min="10" max="200" style="width: 60px; padding: 2px;">
            </div>
            <div style="margin-bottom: 10px;">
                <button id="startBtn" style="padding: 5px 10px; margin-right: 5px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">开始</button>
                <button id="stopBtn" style="padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer;">停止</button>
            </div>
            <div id="statusInfo" style="font-size: 11px; line-height: 1.4;">
                <div>状态: <span id="status">待机</span></div>
                <div>目标速度: <span id="targetSpeedDisplay">60</span> WPM</div>
                <div>当前速度: <span id="currentSpeed">-</span> WPM</div>
                <div>速度调节: <span id="speedAdjustment">待机</span></div>
                <div>调节因子: <span id="adjustmentFactor">1.00</span></div>
                <div>正确率: <span id="accuracy">-</span>%</div>
                <div>剩余时间: <span id="timeLeft">-</span></div>
                <div>当前行: <span id="currentLine">0</span></div>
            </div>
        `;

        document.body.appendChild(controlPanel);

        // 绑定事件
        document.getElementById('startBtn').addEventListener('click', startTyping);
        document.getElementById('stopBtn').addEventListener('click', stopTyping);
        document.getElementById('speedInput').addEventListener('change', function() {
            targetSpeed = parseInt(this.value) || 60;
            document.getElementById('targetSpeedDisplay').textContent = targetSpeed;
        });
    }



    // 获取当前需要输入的文本
    function getCurrentText() {
        const currentDiv = document.querySelector('.typing_on');
        if (!currentDiv) return null;
        
        const textSpan = currentDiv.querySelector('.text span');
        return textSpan ? textSpan.textContent : null;
    }

    // 获取当前输入框
    function getCurrentInput() {
        const currentDiv = document.querySelector('.typing_on');
        if (!currentDiv) return null;
        
        return currentDiv.querySelector('input[type="text"]');
    }

    // 动态速度调节函数
    function adjustSpeed() {
        const speedElement = document.querySelector('.sudu');
        if (!speedElement) return;

        const speedText = speedElement.textContent;
        const speedMatch = speedText.match(/(\d+(?:\.\d+)?)/);

        if (speedMatch) {
            currentActualSpeed = parseFloat(speedMatch[1]);
            speedHistory.push(currentActualSpeed);

            // 保持最近10次的速度记录
            if (speedHistory.length > 10) {
                speedHistory.shift();
            }

            // 计算平均速度（最近5次）
            const recentSpeeds = speedHistory.slice(-5);
            const avgSpeed = recentSpeeds.reduce((a, b) => a + b, 0) / recentSpeeds.length;

            // 纯粹的速度调节模式
            const speedDiff = targetSpeed - avgSpeed;
            const tolerance = targetSpeed * 0.05; // 5%的容差

            if (Math.abs(speedDiff) <= tolerance) {
                adjustmentStatus = '速度稳定';
            } else if (speedDiff > tolerance) {
                // 当前速度低于目标，需要加速
                adjustmentStatus = '加速中';
                speedAdjustmentFactor *= 0.95; // 减少延迟
                speedAdjustmentFactor = Math.max(speedAdjustmentFactor, 0.3); // 最小值限制
            } else {
                // 当前速度高于目标，需要减速
                adjustmentStatus = '减速中';
                speedAdjustmentFactor *= 1.05; // 增加延迟
                speedAdjustmentFactor = Math.min(speedAdjustmentFactor, 3.0); // 最大值限制
            }

            lastSpeedCheckTime = Date.now();
        }
    }

    // 模拟真人输入（带动态速度调节）
    function simulateTyping(text, input, callback) {
        let index = 0;
        const baseDelay = 60000 / (targetSpeed * 5); // 基础延迟，考虑平均字符长度

        function typeNextChar() {
            if (index >= text.length || !isRunning) {
                if (callback) callback();
                return;
            }

            const char = text[index];

            // 应用速度调节因子
            const adjustedBaseDelay = baseDelay * speedAdjustmentFactor;

            // 模拟真人输入的随机延迟
            const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2倍的随机因子
            const delay = adjustedBaseDelay * randomFactor;

            // 某些字符可能需要更长时间
            const extraDelay = /[，。！？；：""''（）]/.test(char) ? delay * 0.2 : 0;

            setTimeout(() => {
                // 模拟按键事件
                const keydownEvent = new KeyboardEvent('keydown', {
                    key: char,
                    code: `Key${char.toUpperCase()}`,
                    bubbles: true
                });

                const inputEvent = new InputEvent('input', {
                    data: char,
                    bubbles: true
                });

                input.dispatchEvent(keydownEvent);
                input.value += char;
                input.dispatchEvent(inputEvent);

                index++;

                // 每输入几个字符检查一次速度
                if (index % 5 === 0) {
                    adjustSpeed();
                }

                typeNextChar();
            }, delay + extraDelay);
        }

        typeNextChar();
    }

    // 移动到下一行
    function moveToNextLine() {
        const currentInput = getCurrentInput();
        if (currentInput) {
            // 模拟回车键
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                bubbles: true
            });
            currentInput.dispatchEvent(enterEvent);
            
            // 触发表单提交或其他必要的事件
            const form = document.getElementById('info_form');
            if (form) {
                const submitEvent = new Event('submit', { bubbles: true });
                form.dispatchEvent(submitEvent);
            }
        }
    }

    // 更新状态信息
    function updateStatus() {
        if (!controlPanel) return;

        // 获取右侧信息
        const speedElement = document.querySelector('.sudu');
        const accuracyElement = document.querySelector('.zhengquelv');
        const timeElement = document.querySelector('.daojishi_time');

        const currentSpeedText = speedElement ? speedElement.textContent : '';
        const accuracyText = accuracyElement ? accuracyElement.textContent : '';
        const timeText = timeElement ? timeElement.textContent : '';

        // 提取数值
        const speedMatch = currentSpeedText.match(/(\d+(?:\.\d+)?)/);
        const accuracyMatch = accuracyText.match(/(\d+(?:\.\d+)?)/);

        document.getElementById('currentSpeed').textContent = speedMatch ? speedMatch[1] : '-';
        document.getElementById('accuracy').textContent = accuracyMatch ? accuracyMatch[1] : '-';
        document.getElementById('timeLeft').textContent = timeText || '-';
        document.getElementById('currentLine').textContent = currentIndex + 1;

        // 更新速度调节信息
        document.getElementById('speedAdjustment').textContent = adjustmentStatus;
        document.getElementById('adjustmentFactor').textContent = speedAdjustmentFactor.toFixed(2);

        // 根据调节状态设置颜色
        const adjustmentElement = document.getElementById('speedAdjustment');
        if (adjustmentStatus === '速度稳定') {
            adjustmentElement.style.color = '#4CAF50';
        } else if (adjustmentStatus === '加速中') {
            adjustmentElement.style.color = '#FF9800';
        } else if (adjustmentStatus === '减速中') {
            adjustmentElement.style.color = '#2196F3';
        } else {
            adjustmentElement.style.color = '#FFF';
        }
    }

    // 开始打字
    function startTyping() {
        if (isRunning) return;

        isRunning = true;
        currentIndex = 0;

        // 重置速度调节相关变量
        speedAdjustmentFactor = 1.0;
        speedHistory = [];
        adjustmentStatus = '初始化';
        currentActualSpeed = 0;
        lastSpeedCheckTime = Date.now();

        document.getElementById('status').textContent = '运行中';
        
        function typeCurrentLine() {
            if (!isRunning) return;
            
            const text = getCurrentText();
            const input = getCurrentInput();
            
            if (!text || !input) {
                console.log('无法找到当前文本或输入框');
                stopTyping();
                return;
            }
            
            // 清空输入框
            input.value = '';
            
            // 开始输入当前行
            simulateTyping(text, input, () => {
                if (!isRunning) return;
                
                // 等待一小段时间后移动到下一行
                setTimeout(() => {
                    moveToNextLine();
                    currentIndex++;
                    
                    // 等待页面更新后继续下一行
                    setTimeout(() => {
                        if (isRunning && getCurrentText()) {
                            typeCurrentLine();
                        } else {
                            stopTyping();
                        }
                    }, 500);
                }, 200 + Math.random() * 300); // 随机延迟
            });
        }
        
        // 开始输入
        typeCurrentLine();
        
        // 定期更新状态和调节速度
        const statusInterval = setInterval(() => {
            if (!isRunning) {
                clearInterval(statusInterval);
                return;
            }
            updateStatus();

            // 每2秒进行一次速度调节检查
            if (Date.now() - lastSpeedCheckTime > 2000) {
                adjustSpeed();
            }
        }, 1000);
    }

    // 停止打字
    function stopTyping() {
        isRunning = false;
        if (typingInterval) {
            clearInterval(typingInterval);
        }

        // 重置状态
        adjustmentStatus = '已停止';
        document.getElementById('status').textContent = '已停止';
        document.getElementById('speedAdjustment').textContent = '已停止';
        document.getElementById('speedAdjustment').style.color = '#FFF';
    }

    // 初始化
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        // 检查是否在打字页面
        if (document.querySelector('.typing_content')) {
            createControlPanel();
            console.log('打字自动化脚本已加载');
        }
    }

    // 启动脚本
    init();

})();
