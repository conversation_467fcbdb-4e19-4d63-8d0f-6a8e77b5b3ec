// ==UserScript==
// @name         打字测试自动化脚本
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动化打字测试，模拟真人输入
// <AUTHOR>
// @match        https://dazi.kukuw.com/*
// @match        http://dazi.kukuw.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    let isRunning = false;
    let targetSpeed = 60; // 默认目标速度 WPM
    let currentIndex = 0;
    let typingInterval;
    let controlPanel;

    // 速度调节相关变量
    let currentActualSpeed = 0;
    let speedAdjustmentFactor = 1.0; // 速度调节因子
    let lastSpeedCheckTime = 0;
    let speedHistory = []; // 速度历史记录
    let adjustmentStatus = '初始化'; // 调节状态

    // 创建控制面板
    function createControlPanel() {
        controlPanel = document.createElement('div');
        controlPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 280px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;

        controlPanel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #4CAF50;">打字自动化控制台</div>
            <div style="margin-bottom: 8px;">
                <label>目标速度 (WPM): </label>
                <input type="number" id="speedInput" value="60" min="1" max="1000" style="width: 60px; padding: 2px;">
            </div>
            <div style="margin-bottom: 10px;">
                <button id="startBtn" style="padding: 5px 10px; margin-right: 5px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">开始</button>
                <button id="stopBtn" style="padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer;">停止</button>
            </div>
            <div id="statusInfo" style="font-size: 11px; line-height: 1.4;">
                <div>状态: <span id="status">待机</span></div>
                <div>目标速度: <span id="targetSpeedDisplay">60</span> WPM</div>
                <div>当前速度: <span id="currentSpeed">-</span> WPM</div>
                <div>速度调节: <span id="speedAdjustment">待机</span></div>
                <div>调节因子: <span id="adjustmentFactor">1.00</span></div>
                <div>正确率: <span id="accuracy">-</span>%</div>
                <div>剩余时间: <span id="timeLeft">-</span></div>
                <div>当前行: <span id="currentLine">0</span></div>
            </div>
        `;

        document.body.appendChild(controlPanel);

        // 绑定事件
        document.getElementById('startBtn').addEventListener('click', startTyping);
        document.getElementById('stopBtn').addEventListener('click', stopTyping);
        document.getElementById('speedInput').addEventListener('change', function() {
            let newTargetSpeed = parseInt(this.value) || 60;

            // 验证速度范围
            if (newTargetSpeed < 1) {
                newTargetSpeed = 1;
                this.value = 1;
                alert('最小速度为 1 WPM');
            } else if (newTargetSpeed > 1000) {
                newTargetSpeed = 1000;
                this.value = 1000;
                alert('最大速度为 1000 WPM');
            }

            // 如果正在运行，根据新目标速度调整当前的调节因子
            if (isRunning && targetSpeed !== newTargetSpeed && targetSpeed > 0) {
                const speedRatio = targetSpeed / Math.max(newTargetSpeed, 1);
                speedAdjustmentFactor *= speedRatio;
                speedAdjustmentFactor = Math.max(0.1, Math.min(10.0, speedAdjustmentFactor));
                console.log(`目标速度从 ${targetSpeed} 改为 ${newTargetSpeed} WPM，调节因子调整为 ${speedAdjustmentFactor.toFixed(2)}`);

                // 清空速度历史，重新开始调节
                speedHistory = [];
                adjustmentStatus = '速度调整中';
            }

            targetSpeed = newTargetSpeed;
            document.getElementById('targetSpeedDisplay').textContent = targetSpeed;

            // 显示理想延迟信息
            const idealDelay = calculateIdealDelay();
            console.log(`目标速度设置为 ${targetSpeed} WPM，理想延迟: ${idealDelay.toFixed(1)}ms/字符`);
        });
    }



    // 获取当前需要输入的文本
    function getCurrentText() {
        const currentDiv = document.querySelector('.typing_on');
        if (!currentDiv) return null;
        
        const textSpan = currentDiv.querySelector('.text span');
        return textSpan ? textSpan.textContent : null;
    }

    // 获取当前输入框
    function getCurrentInput() {
        const currentDiv = document.querySelector('.typing_on');
        if (!currentDiv) return null;
        
        return currentDiv.querySelector('input[type="text"]');
    }

    // 动态速度调节函数（修复减速机制）
    function adjustSpeed() {
        const speedElement = document.querySelector('.sudu');
        if (!speedElement) return;

        const speedText = speedElement.textContent;
        const speedMatch = speedText.match(/(\d+(?:\.\d+)?)/);

        if (speedMatch) {
            currentActualSpeed = parseFloat(speedMatch[1]);
            speedHistory.push(currentActualSpeed);

            // 保持最近6次的速度记录
            if (speedHistory.length > 6) {
                speedHistory.shift();
            }

            // 计算平均速度（最近3次）
            const recentSpeeds = speedHistory.slice(-3);
            const avgSpeed = recentSpeeds.reduce((a, b) => a + b, 0) / recentSpeeds.length;

            // 计算速度差异和相对差异
            const speedDiff = targetSpeed - avgSpeed;
            const relativeDiff = Math.abs(speedDiff) / Math.max(targetSpeed, 1); // 避免除零

            // 动态容差：低速时使用绝对容差，高速时使用相对容差
            const tolerance = targetSpeed <= 20 ? 2 : targetSpeed * 0.06;

            if (Math.abs(speedDiff) <= tolerance) {
                adjustmentStatus = '速度稳定';
            } else if (speedDiff > 0) {
                // 当前速度低于目标，需要加速
                adjustmentStatus = '加速中';

                // 计算加速调节幅度
                let adjustmentRate;
                if (relativeDiff > 0.5) {
                    // 速度差异很大时，大幅调节
                    adjustmentRate = 0.25;
                } else if (relativeDiff > 0.2) {
                    // 速度差异中等时，中等调节
                    adjustmentRate = 0.15;
                } else {
                    // 速度差异较小时，小幅调节
                    adjustmentRate = 0.08;
                }

                speedAdjustmentFactor *= (1 - adjustmentRate);
                speedAdjustmentFactor = Math.max(speedAdjustmentFactor, 0.1); // 降低最小值限制

            } else {
                // 当前速度高于目标，需要减速
                adjustmentStatus = '减速中';

                // 计算减速调节幅度（减速时需要更大的调节幅度）
                let adjustmentRate;
                if (relativeDiff > 0.5) {
                    // 速度差异很大时，大幅减速
                    adjustmentRate = 0.4;
                } else if (relativeDiff > 0.2) {
                    // 速度差异中等时，中等减速
                    adjustmentRate = 0.25;
                } else {
                    // 速度差异较小时，小幅减速
                    adjustmentRate = 0.12;
                }

                speedAdjustmentFactor *= (1 + adjustmentRate);
                speedAdjustmentFactor = Math.min(speedAdjustmentFactor, 10.0); // 增加最大值限制
            }

            // 记录调节信息
            console.log(`速度调节: 目标=${targetSpeed}, 当前=${avgSpeed.toFixed(1)}, 差异=${speedDiff.toFixed(1)}, 因子=${speedAdjustmentFactor.toFixed(2)}, 状态=${adjustmentStatus}`);

            lastSpeedCheckTime = Date.now();
        }
    }

    // 计算基于目标WPM的理想延迟时间（支持极端速度值）
    function calculateIdealDelay() {
        // WPM转换为每字符延迟时间
        // 假设平均每个词5个字符，60秒/分钟
        // 延迟 = 60000ms / (WPM * 5字符/词) = 每字符的毫秒数

        // 确保目标速度至少为1，避免除零错误
        const safeTargetSpeed = Math.max(targetSpeed, 1);
        const idealDelay = 60000 / (safeTargetSpeed * 5);

        // 对于极低速度，设置合理的最大延迟限制（30秒/字符）
        const maxDelay = 30000;
        // 对于极高速度，设置合理的最小延迟限制（10ms/字符）
        const minDelay = 10;

        return Math.max(minDelay, Math.min(maxDelay, idealDelay));
    }

    // 模拟真人输入（带动态速度调节，从第一个字符就按目标速度）
    function simulateTyping(text, input, callback) {
        let index = 0;
        const idealDelay = calculateIdealDelay();

        function typeNextChar() {
            if (index >= text.length || !isRunning) {
                if (callback) callback();
                return;
            }

            const char = text[index];

            // 应用速度调节因子到理想延迟
            const adjustedDelay = idealDelay * speedAdjustmentFactor;

            // 根据目标速度调整随机性：低速时减少随机性，高速时保持随机性
            let randomFactor;
            if (targetSpeed <= 10) {
                // 极低速时，减少随机性以保持精确控制
                randomFactor = 0.95 + Math.random() * 0.1; // 0.95-1.05倍
            } else if (targetSpeed <= 50) {
                // 低速时，适度随机性
                randomFactor = 0.9 + Math.random() * 0.2; // 0.9-1.1倍
            } else {
                // 正常速度时，保持自然随机性
                randomFactor = 0.85 + Math.random() * 0.3; // 0.85-1.15倍
            }

            const delay = adjustedDelay * randomFactor;

            // 某些字符可能需要更长时间（低速时减少额外延迟）
            const extraDelayRate = targetSpeed <= 20 ? 0.05 : 0.15;
            const extraDelay = /[，。！？；：""''（）]/.test(char) ? delay * extraDelayRate : 0;

            setTimeout(() => {
                // 模拟按键事件
                const keydownEvent = new KeyboardEvent('keydown', {
                    key: char,
                    code: `Key${char.toUpperCase()}`,
                    bubbles: true
                });

                const inputEvent = new InputEvent('input', {
                    data: char,
                    bubbles: true
                });

                input.dispatchEvent(keydownEvent);
                input.value += char;
                input.dispatchEvent(inputEvent);

                index++;

                // 根据目标速度调整检查频率
                let checkFrequency;
                if (targetSpeed <= 10) {
                    checkFrequency = 2; // 极低速时每2个字符检查
                } else if (targetSpeed <= 30) {
                    checkFrequency = 3; // 低速时每3个字符检查
                } else {
                    checkFrequency = 5; // 正常速度时每5个字符检查
                }

                if (index % checkFrequency === 0) {
                    adjustSpeed();
                }

                typeNextChar();
            }, delay + extraDelay);
        }

        typeNextChar();
    }

    // 移动到下一行
    function moveToNextLine() {
        const currentInput = getCurrentInput();
        if (currentInput) {
            // 模拟回车键
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                bubbles: true
            });
            currentInput.dispatchEvent(enterEvent);
            
            // 触发表单提交或其他必要的事件
            const form = document.getElementById('info_form');
            if (form) {
                const submitEvent = new Event('submit', { bubbles: true });
                form.dispatchEvent(submitEvent);
            }
        }
    }

    // 更新状态信息
    function updateStatus() {
        if (!controlPanel) return;

        // 获取右侧信息
        const speedElement = document.querySelector('.sudu');
        const accuracyElement = document.querySelector('.zhengquelv');
        const timeElement = document.querySelector('.daojishi_time');

        const currentSpeedText = speedElement ? speedElement.textContent : '';
        const accuracyText = accuracyElement ? accuracyElement.textContent : '';
        const timeText = timeElement ? timeElement.textContent : '';

        // 提取数值
        const speedMatch = currentSpeedText.match(/(\d+(?:\.\d+)?)/);
        const accuracyMatch = accuracyText.match(/(\d+(?:\.\d+)?)/);

        document.getElementById('currentSpeed').textContent = speedMatch ? speedMatch[1] : '-';
        document.getElementById('accuracy').textContent = accuracyMatch ? accuracyMatch[1] : '-';
        document.getElementById('timeLeft').textContent = timeText || '-';
        document.getElementById('currentLine').textContent = currentIndex + 1;

        // 更新速度调节信息
        document.getElementById('speedAdjustment').textContent = adjustmentStatus;
        document.getElementById('adjustmentFactor').textContent = speedAdjustmentFactor.toFixed(2);

        // 根据调节状态设置颜色
        const adjustmentElement = document.getElementById('speedAdjustment');
        if (adjustmentStatus === '速度稳定') {
            adjustmentElement.style.color = '#4CAF50';
        } else if (adjustmentStatus === '加速中') {
            adjustmentElement.style.color = '#FF9800';
        } else if (adjustmentStatus === '减速中') {
            adjustmentElement.style.color = '#2196F3';
        } else {
            adjustmentElement.style.color = '#FFF';
        }
    }

    // 开始打字
    function startTyping() {
        if (isRunning) return;

        isRunning = true;
        currentIndex = 0;

        // 重置速度调节相关变量
        speedAdjustmentFactor = 1.0; // 从理想速度开始
        speedHistory = [];
        adjustmentStatus = '初始化';
        currentActualSpeed = 0;
        lastSpeedCheckTime = Date.now();

        document.getElementById('status').textContent = '运行中';

        // 显示初始目标速度信息
        console.log(`开始打字，目标速度: ${targetSpeed} WPM，理想延迟: ${calculateIdealDelay().toFixed(1)}ms/字符`);
        
        function typeCurrentLine() {
            if (!isRunning) return;
            
            const text = getCurrentText();
            const input = getCurrentInput();
            
            if (!text || !input) {
                console.log('无法找到当前文本或输入框');
                stopTyping();
                return;
            }
            
            // 清空输入框
            input.value = '';
            
            // 开始输入当前行
            simulateTyping(text, input, () => {
                if (!isRunning) return;
                
                // 等待一小段时间后移动到下一行
                setTimeout(() => {
                    moveToNextLine();
                    currentIndex++;
                    
                    // 等待页面更新后继续下一行
                    setTimeout(() => {
                        if (isRunning && getCurrentText()) {
                            typeCurrentLine();
                        } else {
                            stopTyping();
                        }
                    }, 500);
                }, 200 + Math.random() * 300); // 随机延迟
            });
        }
        
        // 开始输入
        typeCurrentLine();
        
        // 定期更新状态和调节速度
        const statusInterval = setInterval(() => {
            if (!isRunning) {
                clearInterval(statusInterval);
                return;
            }
            updateStatus();

            // 每1.5秒进行一次速度调节检查（更频繁的检查）
            if (Date.now() - lastSpeedCheckTime > 1500) {
                adjustSpeed();
            }
        }, 1000);
    }

    // 停止打字
    function stopTyping() {
        isRunning = false;
        if (typingInterval) {
            clearInterval(typingInterval);
        }

        // 重置状态
        adjustmentStatus = '已停止';
        document.getElementById('status').textContent = '已停止';
        document.getElementById('speedAdjustment').textContent = '已停止';
        document.getElementById('speedAdjustment').style.color = '#FFF';
    }

    // 初始化
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }
        
        // 检查是否在打字页面
        if (document.querySelector('.typing_content')) {
            createControlPanel();
            console.log('打字自动化脚本已加载');
        }
    }

    // 启动脚本
    init();

})();
